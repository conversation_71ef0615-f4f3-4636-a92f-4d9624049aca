<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Role;
use App\Models\Permission;
use App\Models\Menu;
use App\Models\PermissionMenu;
use App\Models\User;
use App\Models\Personne;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class RolePermissionSeeder extends Seeder
{
    public function run()
    {
        DB::beginTransaction();
        
        try {
            // Créer les permissions de base
            $permissions = [
                ['permission_name' => 'Créer'],
                ['permission_name' => 'Lire'],
                ['permission_name' => 'Modifier'],
                ['permission_name' => 'Supprimer'],
            ];

            foreach ($permissions as $permission) {
                Permission::firstOrCreate(['permission_name' => $permission['permission_name']], $permission);
            }

            // Créer les rôles
            $roles = [
                ['role_name' => 'Administrateur'],
                ['role_name' => 'Gestionnaire'],
                ['role_name' => 'Utilisateur'],
            ];

            foreach ($roles as $role) {
                Role::firstOrCreate(['role_name' => $role['role_name']], $role);
            }

            // Créer les menus de base
            $menus = [
                ['libelle' => 'Dashboard', 'url' => '/dashboard', 'icon' => 'educate-icon educate-home icon-wrap', 'ordre' => 1],
                ['libelle' => 'Gestion des rôles', 'url' => '/role', 'icon' => 'educate-icon educate-department icon-wrap', 'ordre' => 2],
                ['libelle' => 'Gestion des utilisateurs', 'url' => '/utilisateurs', 'icon' => 'educate-icon educate-professor icon-wrap', 'ordre' => 3],
                ['libelle' => 'Type de réforme', 'url' => '/typereforme', 'icon' => 'educate-icon educate-course icon-wrap', 'ordre' => 4],
                ['libelle' => 'Gestion des réformes', 'url' => '/reforme', 'icon' => 'educate-icon educate-library icon-wrap', 'ordre' => 5],
                ['libelle' => 'Gestion des Indicateurs', 'url' => '/indicateurs', 'icon' => 'educate-icon educate-data-table icon-wrap', 'ordre' => 6],
                ['libelle' => 'Activités de réforme', 'url' => '/activites', 'icon' => 'educate-icon educate-event icon-wrap', 'ordre' => 7],
            ];

            foreach ($menus as $menu) {
                Menu::firstOrCreate(['url' => $menu['url']], $menu);
            }

            // Créer les associations permission-menu
            $this->createPermissionMenus();

            // Assigner les permissions aux rôles
            $this->assignPermissionsToRoles();

            // Créer des utilisateurs de test
            $this->createTestUsers();

            DB::commit();
            
            $this->command->info('Seeders de rôles et permissions créés avec succès !');
            
        } catch (\Exception $e) {
            DB::rollback();
            $this->command->error('Erreur lors de la création des seeders : ' . $e->getMessage());
            throw $e;
        }
    }

    private function createPermissionMenus()
    {
        $menus = Menu::all();
        $permissions = Permission::all();

        foreach ($menus as $menu) {
            foreach ($permissions as $permission) {
                // Tous les menus ont au moins la permission "Lire"
                if ($permission->permission_name === 'Lire') {
                    PermissionMenu::firstOrCreate([
                        'menu_id' => $menu->id,
                        'permission_id' => $permission->id,
                    ]);
                }
                
                // Les menus administratifs ont toutes les permissions
                if (in_array($menu->url, ['/role', '/utilisateurs', '/typereforme', '/reforme', '/indicateurs', '/activites'])) {
                    PermissionMenu::firstOrCreate([
                        'menu_id' => $menu->id,
                        'permission_id' => $permission->id,
                    ]);
                }
            }
        }
    }

    private function assignPermissionsToRoles()
    {
        $adminRole = Role::where('role_name', 'Administrateur')->first();
        $managerRole = Role::where('role_name', 'Gestionnaire')->first();
        $userRole = Role::where('role_name', 'Utilisateur')->first();

        // ADMINISTRATEUR : Toutes les permissions sur tous les menus
        $allPermissionMenus = PermissionMenu::all();
        $adminRole->permissionMenus()->sync($allPermissionMenus->pluck('id'));

        // GESTIONNAIRE : Permissions granulaires
        $managerPermissions = [];

        // Dashboard : Lecture seule
        $dashboardPermissions = PermissionMenu::whereHas('menu', function ($query) {
            $query->where('url', '/dashboard');
        })->whereHas('permission', function ($query) {
            $query->where('permission_name', 'Lire');
        })->get();
        $managerPermissions = array_merge($managerPermissions, $dashboardPermissions->pluck('id')->toArray());

        // Activités : Toutes les permissions sauf suppression
        $activitesPermissions = PermissionMenu::whereHas('menu', function ($query) {
            $query->where('url', '/activites');
        })->whereHas('permission', function ($query) {
            $query->whereIn('permission_name', ['Lire', 'Créer', 'Modifier']);
        })->get();
        $managerPermissions = array_merge($managerPermissions, $activitesPermissions->pluck('id')->toArray());

        // Réformes : Toutes les permissions sauf suppression
        $reformesPermissions = PermissionMenu::whereHas('menu', function ($query) {
            $query->where('url', '/reforme');
        })->whereHas('permission', function ($query) {
            $query->whereIn('permission_name', ['Lire', 'Créer', 'Modifier']);
        })->get();
        $managerPermissions = array_merge($managerPermissions, $reformesPermissions->pluck('id')->toArray());

        // Indicateurs : Toutes les permissions sauf suppression
        $indicateursPermissions = PermissionMenu::whereHas('menu', function ($query) {
            $query->where('url', '/indicateurs');
        })->whereHas('permission', function ($query) {
            $query->whereIn('permission_name', ['Lire', 'Créer', 'Modifier']);
        })->get();
        $managerPermissions = array_merge($managerPermissions, $indicateursPermissions->pluck('id')->toArray());

        // Type de réforme : Lecture et modification seulement
        $typeReformePermissions = PermissionMenu::whereHas('menu', function ($query) {
            $query->where('url', '/typereforme');
        })->whereHas('permission', function ($query) {
            $query->whereIn('permission_name', ['Lire', 'Modifier']);
        })->get();
        $managerPermissions = array_merge($managerPermissions, $typeReformePermissions->pluck('id')->toArray());

        $managerRole->permissionMenus()->sync(array_unique($managerPermissions));

        // UTILISATEUR : Lecture seule sur dashboard et activités
        $userPermissions = [];

        // Dashboard : Lecture seule
        $userPermissions = array_merge($userPermissions, $dashboardPermissions->pluck('id')->toArray());

        // Activités : Lecture seule
        $activitesReadPermissions = PermissionMenu::whereHas('menu', function ($query) {
            $query->where('url', '/activites');
        })->whereHas('permission', function ($query) {
            $query->where('permission_name', 'Lire');
        })->get();
        $userPermissions = array_merge($userPermissions, $activitesReadPermissions->pluck('id')->toArray());

        $userRole->permissionMenus()->sync(array_unique($userPermissions));
    }

    private function createTestUsers()
    {
        $testUsers = [
            [
                'nom' => 'Admin',
                'prenom' => 'Super',
                'fonction' => 'Administrateur Système',
                'tel' => '1234567890',
                'email' => '<EMAIL>',
                'password' => 'admin123',
                'role' => 'Administrateur'
            ],
            [
                'nom' => 'Manager',
                'prenom' => 'Test',
                'fonction' => 'Gestionnaire',
                'tel' => '1234567891',
                'email' => '<EMAIL>',
                'password' => 'manager123',
                'role' => 'Gestionnaire'
            ],
            [
                'nom' => 'User',
                'prenom' => 'Simple',
                'fonction' => 'Utilisateur',
                'tel' => '1234567892',
                'email' => '<EMAIL>',
                'password' => 'user123',
                'role' => 'Utilisateur'
            ]
        ];

        foreach ($testUsers as $userData) {
            // Créer la personne
            $personne = Personne::firstOrCreate(
                ['email' => $userData['email']],
                [
                    'nom' => $userData['nom'],
                    'prenom' => $userData['prenom'],
                    'fonction' => $userData['fonction'],
                    'tel' => $userData['tel'],
                ]
            );

            // Créer l'utilisateur
            $user = User::firstOrCreate(
                ['personne_id' => $personne->id],
                [
                    'pwd' => Hash::make($userData['password']),
                    'status' => 1,
                ]
            );

            // Assigner le rôle
            $role = Role::where('role_name', $userData['role'])->first();
            if ($role && !$user->roles()->where('role_id', $role->id)->exists()) {
                $user->roles()->attach($role->id);
            }
        }
    }
}
