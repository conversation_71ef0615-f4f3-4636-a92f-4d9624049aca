@extends('layout.app')

@section('content')

<div class="container pt-4">
    @if(session('success'))
    <div class="alert alert-success">{{ session('success') }}</div>
    @endif
    
    @if(session('error'))
    <div class="alert alert-danger">{{ session('error') }}</div>
    @endif
    
    @if($errors->any())
    <div class="alert alert-danger">
        <ul class="mb-0">
            @foreach($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
    @endif

    <!-- Mobile Menu end -->
    <div class="breadcome-area">
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="breadcome-list">
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <div class="breadcome-heading">
                                    <form role="search" class="sr-input-func">
                                        <input type="text" placeholder="Search..." class="search-int form-control">
                                        <a href="#"><i class="fa fa-search"></i></a>
                                    </form>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <ul class="breadcome-menu">
                                    <li><span class="bread-blod">Mon Profil</span></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Informations du profil -->
    <div class="data-table-area mg-b-15">
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="sparkline13-list">
                        <div class="sparkline13-hd">
                            <div class="main-sparkline13-hd">
                                <h1>Informations <span class="table-project-n">du </span>Profil</h1>
                                <button type="button" class="btn btn-warning mb-3" data-toggle="modal" data-target="#editProfileModal">
                                    <i class="fa fa-edit"></i> Modifier mon profil
                                </button>
                            </div>
                        </div>
                        <div class="sparkline13-graph">
                            <div class="datatable-dashv1-list custom-datatable-overright">
                                <div class="row">
                                    <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                        <div class="mb-3">
                                            <label><strong>Nom complet :</strong></label>
                                            <p class="form-control-static">{{ $user->personne->nom ?? '' }} {{ $user->personne->prenom ?? '' }}</p>
                                        </div>
                                        <div class="mb-3">
                                            <label><strong>Email :</strong></label>
                                            <p class="form-control-static">{{ $user->personne->email ?? 'Non renseigné' }}</p>
                                        </div>
                                        <div class="mb-3">
                                            <label><strong>Fonction :</strong></label>
                                            <p class="form-control-static">{{ $user->personne->fonction ?? 'Non renseigné' }}</p>
                                        </div>
                                    </div>
                                    <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                        <div class="mb-3">
                                            <label><strong>Téléphone :</strong></label>
                                            <p class="form-control-static">{{ $user->personne->tel ?? 'Non renseigné' }}</p>
                                        </div>
                                        <div class="mb-3">
                                            <label><strong>Rôle :</strong></label>
                                            <p class="form-control-static">
                                                <span class="badge badge-primary">{{ $user->roles->first()->role_name ?? 'Aucun rôle' }}</span>
                                            </p>
                                        </div>
                                        <div class="mb-3">
                                            <label><strong>Membre depuis :</strong></label>
                                            <p class="form-control-static">{{ $user->created_at ? $user->created_at->format('d/m/Y') : 'Non renseigné' }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de modification du profil -->
<div class="modal fade" id="editProfileModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog modal-lg" role="document">
        <form action="{{ route('profile.update') }}" method="POST">
            @csrf @method('PUT')
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <ul id="myTabedu1" class="tab-review-design">
                        <li class="active"><a href="#description">Modifier mon profil</a></li>
                    </ul>
                </div>
                <div class="modal-body">
                    @if($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                    
                    <div class="row">
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                            <div class="mb-3">
                                <label>Nom *</label>
                                <input type="text" class="form-control @error('nom') is-invalid @enderror" name="nom" value="{{ old('nom', $user->personne->nom ?? '') }}" required>
                                @error('nom')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="mb-3">
                                <label>Prénom *</label>
                                <input type="text" class="form-control @error('prenom') is-invalid @enderror" name="prenom" value="{{ old('prenom', $user->personne->prenom ?? '') }}" required>
                                @error('prenom')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="mb-3">
                                <label>Email *</label>
                                <input type="email" class="form-control @error('email') is-invalid @enderror" name="email" value="{{ old('email', $user->personne->email ?? '') }}" required>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="mb-3">
                                <label>Fonction</label>
                                <input type="text" class="form-control @error('fonction') is-invalid @enderror" name="fonction" value="{{ old('fonction', $user->personne->fonction ?? '') }}">
                                @error('fonction')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                            <div class="mb-3">
                                <label>Téléphone</label>
                                <input type="text" class="form-control @error('tel') is-invalid @enderror" name="tel" value="{{ old('tel', $user->personne->tel ?? '') }}">
                                @error('tel')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="mb-3">
                                <label>Mot de passe actuel *</label>
                                <input type="password" class="form-control @error('current_password') is-invalid @enderror" name="current_password" required>
                                @error('current_password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Saisissez votre mot de passe actuel pour confirmer les modifications.</small>
                            </div>
                            <div class="mb-3">
                                <label>Nouveau mot de passe</label>
                                <input type="password" class="form-control @error('new_password') is-invalid @enderror" name="new_password">
                                @error('new_password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="mb-3">
                                <label>Confirmer le nouveau mot de passe</label>
                                <input type="password" class="form-control @error('new_password_confirmation') is-invalid @enderror" name="new_password_confirmation">
                                @error('new_password_confirmation')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Enregistrer</button>
                </div>
            </div>
        </form>
    </div>
</div>

@endsection 