<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Reforme;
use App\Models\Typereforme;
use App\Models\Activitesreformes;
use App\Models\Indicateur;
use App\Models\EvolutionIndicateur;
use App\Models\ReformeIndicateur;
use App\Models\Session;
use App\Models\Notification;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Middleware d'authentification
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Affiche le tableau de bord avec les statistiques améliorées
     */
    public function index()
    {
        // Statistiques principales demandées
        $stats = $this->getMainStatistics();

        // Statistiques complémentaires pour l'affichage
        $stats['reformesParType'] = $this->getReformesParType();
        $stats['activitesRecentes'] = $this->getActivitesRecentes();
        $stats['reformesParStatut'] = $this->getReformesParStatut();
        $stats['tendanceActivites'] = $this->getTendanceActivites();
        $stats['notificationsRecentes'] = $this->getNotificationsRecentes();

        // Statistiques de suivi des indicateurs
        $stats['statistiquesIndicateurs'] = $this->getStatistiquesIndicateurs();
        $stats['evolutionsRecentes'] = $this->getEvolutionsRecentes();
        $stats['indicateursActifs'] = $this->getIndicateursActifs();

        return view('dashboard', $stats);
    }

    /**
     * Calcule les statistiques principales demandées
     */
    private function getMainStatistics()
    {
        // 1. Nombre d'utilisateurs connectés (sessions actives)
        $utilisateursConnectes = Session::active()
            ->where('last_activity', '>=', Carbon::now()->subMinutes(30))
            ->distinct('user_id')
            ->count('user_id');

        // 2. Activités validées (statut 'A' = Achevé)
        $totalActivites = Activitesreformes::count();
        $activitesValidees = Activitesreformes::where('statut', 'A')->count();
        $pourcentageActivites = $totalActivites > 0 ? round(($activitesValidees / $totalActivites) * 100, 1) : 0;

        // 3. Réformes validées (statut 'Achevé')
        $totalReformes = Reforme::count();
        $reformesValidees = Reforme::where(function($query) {
            $query->where('statut_manuel', 'Achevé')
                  ->orWhere(function($q) {
                      $q->whereNull('statut_manuel')
                        ->whereNotNull('date_fin');
                  });
        })->count();
        $pourcentageReformes = $totalReformes > 0 ? round(($reformesValidees / $totalReformes) * 100, 1) : 0;

        // Statistiques supplémentaires
        $totalUtilisateurs = User::count();
        $totalIndicateurs = Indicateur::count();

        return [
            // Statistiques principales demandées
            'utilisateursConnectes' => $utilisateursConnectes,
            'totalActivites' => $totalActivites,
            'activitesValidees' => $activitesValidees,
            'pourcentageActivites' => $pourcentageActivites,
            'totalReformes' => $totalReformes,
            'reformesValidees' => $reformesValidees,
            'pourcentageReformes' => $pourcentageReformes,

            // Statistiques complémentaires
            'totalUtilisateurs' => $totalUtilisateurs,
            'totalIndicateurs' => $totalIndicateurs,

            // Données pour les graphiques
            'activitesEnCours' => Activitesreformes::where('statut', 'C')->count(),
            'activitesEnPause' => Activitesreformes::where('statut', 'P')->count(),
            'reformesEnCours' => Reforme::where(function($query) {
                $query->where('statut_manuel', 'En cours')
                      ->orWhere(function($q) {
                          $q->whereNull('statut_manuel')
                            ->where('date_debut', '<=', now())
                            ->whereNull('date_fin');
                      });
            })->count(),
            'reformesEnPause' => Reforme::where('statut_manuel', 'En pause')->count(),
        ];
    }

    /**
     * Récupère le nombre de réformes par type
     */
    private function getReformesParType()
    {
        return DB::table('reformes')
            ->join('type_reforme', 'reformes.type_reforme', '=', 'type_reforme.id')
            ->select('type_reforme.lib', DB::raw('count(*) as total'))
            ->groupBy('type_reforme.lib')
            ->get();
    }

    /**
     * Récupère les réformes par statut pour les graphiques
     */
    private function getReformesParStatut()
    {
        $reformes = Reforme::all();
        $statuts = [];

        foreach ($reformes as $reforme) {
            $statut = $reforme->statut; // Utilise l'accesseur qui gère la priorité manuel/auto
            $statuts[$statut] = ($statuts[$statut] ?? 0) + 1;
        }

        return $statuts;
    }

    /**
     * Récupère les activités récentes avec plus d'informations
     */
    private function getActivitesRecentes()
    {
        return Activitesreformes::with(['reforme', 'creator.personne'])
            ->orderBy('created_at', 'desc')
            ->limit(6)
            ->get();
    }

    /**
     * Calcule la tendance des activités sur les 30 derniers jours
     */
    private function getTendanceActivites()
    {
        $dernierMois = Carbon::now()->subDays(30);

        return [
            'nouvelles' => Activitesreformes::where('created_at', '>=', $dernierMois)->count(),
            'terminees' => Activitesreformes::where('statut', 'A')
                ->where('date_fin', '>=', $dernierMois)
                ->count(),
            'en_cours' => Activitesreformes::where('statut', 'C')->count(),
        ];
    }

    /**
     * Récupère les notifications récentes pour le dashboard
     */
    private function getNotificationsRecentes()
    {
        try {
            return DB::table('notifications')
                ->where('created_at', '>=', Carbon::now()->subDays(7))
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get();
        } catch (\Exception $e) {
            return collect(); // Retourne une collection vide si la table n'existe pas
        }
    }

    /**
     * API endpoint pour les statistiques en temps réel
     */
    public function getStatsApi()
    {
        $stats = $this->getMainStatistics();

        return response()->json([
            'utilisateurs_connectes' => $stats['utilisateursConnectes'],
            'activites_validees' => $stats['activitesValidees'],
            'total_activites' => $stats['totalActivites'],
            'pourcentage_activites' => $stats['pourcentageActivites'],
            'reformes_validees' => $stats['reformesValidees'],
            'total_reformes' => $stats['totalReformes'],
            'pourcentage_reformes' => $stats['pourcentageReformes'],
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * Récupère les statistiques générales des indicateurs
     */
    private function getStatistiquesIndicateurs()
    {
        $totalIndicateurs = Indicateur::count();
        $indicateursActifs = ReformeIndicateur::distinct('indicateur_id')->count();
        $totalMesures = EvolutionIndicateur::count();
        $mesuresRecentes = EvolutionIndicateur::where('created_at', '>=', Carbon::now()->subDays(30))->count();

        return [
            'total_indicateurs' => $totalIndicateurs,
            'indicateurs_actifs' => $indicateursActifs,
            'total_mesures' => $totalMesures,
            'mesures_recentes' => $mesuresRecentes,
            'pourcentage_actifs' => $totalIndicateurs > 0 ? round(($indicateursActifs / $totalIndicateurs) * 100, 1) : 0
        ];
    }

    /**
     * Récupère les évolutions récentes des indicateurs
     */
    private function getEvolutionsRecentes()
    {
        return EvolutionIndicateur::with(['reformeIndicateur.reforme', 'reformeIndicateur.indicateur'])
            ->orderBy('date_evolution', 'desc')
            ->limit(10)
            ->get()
            ->map(function($evolution) {
                return [
                    'date' => $evolution->date_evolution->format('d/m/Y'),
                    'indicateur' => $evolution->reformeIndicateur->indicateur->libelle ?? 'N/A',
                    'reforme' => $evolution->reformeIndicateur->reforme->titre ?? 'N/A',
                    'valeur' => $evolution->valeur,
                    'unite' => $evolution->reformeIndicateur->indicateur->unite ?? '',
                    'tendance' => $evolution->tendance
                ];
            });
    }

    /**
     * Récupère les indicateurs les plus actifs
     */
    private function getIndicateursActifs()
    {
        return DB::table('evolution_indicateurs')
            ->join('reformes_indicateurs', 'evolution_indicateurs.reforme_indicateur_id', '=', 'reformes_indicateurs.id')
            ->join('indicateurs', 'reformes_indicateurs.indicateur_id', '=', 'indicateurs.id')
            ->select('indicateurs.libelle', 'indicateurs.unite', DB::raw('COUNT(*) as nb_mesures'))
            ->where('evolution_indicateurs.created_at', '>=', Carbon::now()->subDays(90))
            ->groupBy('indicateurs.id', 'indicateurs.libelle', 'indicateurs.unite')
            ->orderBy('nb_mesures', 'desc')
            ->limit(5)
            ->get();
    }

    /**
     * Page dédiée au suivi des indicateurs
     */
    public function suiviIndicateurs()
    {
        $statistiques = $this->getStatistiquesIndicateurs();
        $evolutionsRecentes = $this->getEvolutionsRecentes();
        $indicateursActifs = $this->getIndicateursActifs();

        // Données pour les graphiques
        $tendancesIndicateurs = $this->getTendancesIndicateurs();
        $repartitionParReforme = $this->getRepartitionIndicateursParReforme();

        return view('dashboard.suivi-indicateurs', compact(
            'statistiques',
            'evolutionsRecentes',
            'indicateursActifs',
            'tendancesIndicateurs',
            'repartitionParReforme'
        ));
    }

    /**
     * Récupère les tendances des indicateurs sur les 6 derniers mois
     */
    private function getTendancesIndicateurs()
    {
        $sixMoisAgo = Carbon::now()->subMonths(6);

        return DB::table('evolution_indicateurs')
            ->join('reformes_indicateurs', 'evolution_indicateurs.reforme_indicateur_id', '=', 'reformes_indicateurs.id')
            ->join('indicateurs', 'reformes_indicateurs.indicateur_id', '=', 'indicateurs.id')
            ->select(
                'indicateurs.libelle',
                DB::raw('DATE_FORMAT(evolution_indicateurs.date_evolution, "%Y-%m") as mois'),
                DB::raw('AVG(evolution_indicateurs.valeur) as valeur_moyenne')
            )
            ->where('evolution_indicateurs.date_evolution', '>=', $sixMoisAgo)
            ->groupBy('indicateurs.id', 'indicateurs.libelle', 'mois')
            ->orderBy('mois')
            ->get()
            ->groupBy('libelle');
    }

    /**
     * Récupère la répartition des indicateurs par réforme
     */
    private function getRepartitionIndicateursParReforme()
    {
        return DB::table('reformes_indicateurs')
            ->join('reformes', 'reformes_indicateurs.reforme_id', '=', 'reformes.id')
            ->select('reformes.titre', DB::raw('COUNT(*) as nb_indicateurs'))
            ->groupBy('reformes.id', 'reformes.titre')
            ->orderBy('nb_indicateurs', 'desc')
            ->limit(10)
            ->get();
    }

    /**
     * API pour les données de suivi des indicateurs
     */
    public function getSuiviIndicateursApi()
    {
        $statistiques = $this->getStatistiquesIndicateurs();
        $evolutionsRecentes = $this->getEvolutionsRecentes();

        return response()->json([
            'statistiques' => $statistiques,
            'evolutions_recentes' => $evolutionsRecentes,
            'timestamp' => now()->toISOString()
        ]);
    }
}