<?php

namespace App\Listeners;

use Illuminate\Auth\Events\Login;
use Illuminate\Auth\Events\Logout;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Models\Session;
use Carbon\Carbon;

class UserSessionListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle user login event.
     */
    public function handleLogin(Login $event): void
    {
        // Marquer les anciennes sessions de cet utilisateur comme inactives
        Session::where('user_id', $event->user->id)
               ->where('status', 'active')
               ->update([
                   'status' => 'inactive',
                   'logout_at' => Carbon::now()
               ]);

        // Créer une nouvelle session
        Session::create([
            'user_id' => $event->user->id,
            'session_id' => session()->getId(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->header('User-Agent', ''),
            'login_at' => Carbon::now(),
            'last_activity' => Carbon::now(),
            'status' => 'active'
        ]);
    }

    /**
     * Handle user logout event.
     */
    public function handleLogout(Logout $event): void
    {
        if ($event->user) {
            Session::where('user_id', $event->user->id)
                   ->where('session_id', session()->getId())
                   ->where('status', 'active')
                   ->update([
                       'status' => 'inactive',
                       'logout_at' => Carbon::now()
                   ]);
        }
    }

    /**
     * Register the listeners for the subscriber.
     */
    public function subscribe($events): void
    {
        $events->listen(
            Login::class,
            [UserSessionListener::class, 'handleLogin']
        );

        $events->listen(
            Logout::class,
            [UserSessionListener::class, 'handleLogout']
        );
    }
}
