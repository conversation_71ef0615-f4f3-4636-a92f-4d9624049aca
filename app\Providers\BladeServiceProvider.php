<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Auth;

class BladeServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Directive pour vérifier si l'utilisateur a un rôle
        Blade::if('hasRole', function ($role) {
            return Auth::check() && Auth::user()->hasRole($role);
        });

        // Directive pour vérifier si l'utilisateur a l'un des rôles
        Blade::if('hasAnyRole', function (...$roles) {
            return Auth::check() && Auth::user()->hasAnyRole($roles);
        });

        // Directive pour vérifier si l'utilisateur a tous les rôles
        Blade::if('hasAllRoles', function (...$roles) {
            return Auth::check() && Auth::user()->hasAllRoles($roles);
        });

        // Directive pour vérifier si l'utilisateur a une permission
        Blade::if('hasPermission', function ($menuId, $permissionName) {
            return Auth::check() && Auth::user()->hasPermission($menuId, $permissionName);
        });

        // Directive pour vérifier si l'utilisateur peut accéder à un menu
        Blade::if('canAccessMenu', function ($menuId) {
            return Auth::check() && Auth::user()->canAccessMenu($menuId);
        });

        // Directive pour vérifier si l'utilisateur a une permission par nom
        Blade::if('hasPermissionByName', function ($permissionName) {
            return Auth::check() && Auth::user()->hasPermissionByName($permissionName);
        });

        // Directive pour vérifier si l'utilisateur est administrateur
        Blade::if('isAdmin', function () {
            return Auth::check() && Auth::user()->hasRole('Administrateur');
        });

        // Directive pour vérifier si l'utilisateur est gestionnaire ou administrateur
        Blade::if('isManager', function () {
            return Auth::check() && Auth::user()->hasAnyRole(['Administrateur', 'Gestionnaire']);
        });

        // Directive pour vérifier si l'utilisateur peut créer
        Blade::if('canCreate', function () {
            return Auth::check() && Auth::user()->hasPermissionByName('Créer');
        });

        // Directive pour vérifier si l'utilisateur peut modifier
        Blade::if('canEdit', function () {
            return Auth::check() && Auth::user()->hasPermissionByName('Modifier');
        });

        // Directive pour vérifier si l'utilisateur peut supprimer
        Blade::if('canDelete', function () {
            return Auth::check() && Auth::user()->hasPermissionByName('Supprimer');
        });

        // Directive pour vérifier si l'utilisateur peut lire
        Blade::if('canRead', function () {
            return Auth::check() && Auth::user()->hasPermissionByName('Lire');
        });

        // Directives pour les permissions CRUD par menu ID
        Blade::if('canCreateMenu', function ($menuId) {
            return Auth::check() && Auth::user()->canCreate($menuId);
        });

        Blade::if('canEditMenu', function ($menuId) {
            return Auth::check() && Auth::user()->canEdit($menuId);
        });

        Blade::if('canDeleteMenu', function ($menuId) {
            return Auth::check() && Auth::user()->canDelete($menuId);
        });

        Blade::if('canReadMenu', function ($menuId) {
            return Auth::check() && Auth::user()->canRead($menuId);
        });

        // Directives pour les permissions CRUD par URL
        Blade::if('canCreateUrl', function ($url) {
            return Auth::check() && Auth::user()->canCreateForUrl($url);
        });

        Blade::if('canEditUrl', function ($url) {
            return Auth::check() && Auth::user()->canEditForUrl($url);
        });

        Blade::if('canDeleteUrl', function ($url) {
            return Auth::check() && Auth::user()->canDeleteForUrl($url);
        });

        Blade::if('canReadUrl', function ($url) {
            return Auth::check() && Auth::user()->canReadForUrl($url);
        });

        // Directives spécifiques pour les modules principaux
        Blade::if('canManageActivites', function () {
            return Auth::check() && Auth::user()->canReadForUrl('/activites');
        });

        Blade::if('canCreateActivites', function () {
            return Auth::check() && Auth::user()->canCreateForUrl('/activites');
        });

        Blade::if('canEditActivites', function () {
            return Auth::check() && Auth::user()->canEditForUrl('/activites');
        });

        Blade::if('canDeleteActivites', function () {
            return Auth::check() && Auth::user()->canDeleteForUrl('/activites');
        });

        // Directives pour les réformes
        Blade::if('canManageReformes', function () {
            return Auth::check() && Auth::user()->canReadForUrl('/reforme');
        });

        Blade::if('canCreateReformes', function () {
            return Auth::check() && Auth::user()->canCreateForUrl('/reforme');
        });

        Blade::if('canEditReformes', function () {
            return Auth::check() && Auth::user()->canEditForUrl('/reforme');
        });

        Blade::if('canDeleteReformes', function () {
            return Auth::check() && Auth::user()->canDeleteForUrl('/reforme');
        });
    }
}
