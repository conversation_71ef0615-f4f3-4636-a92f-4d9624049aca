<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Auth;

class BladeServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Directive pour vérifier si l'utilisateur a un rôle
        Blade::if('hasRole', function ($role) {
            return Auth::check() && Auth::user()->hasRole($role);
        });

        // Directive pour vérifier si l'utilisateur a l'un des rôles
        Blade::if('hasAnyRole', function (...$roles) {
            return Auth::check() && Auth::user()->hasAnyRole($roles);
        });

        // Directive pour vérifier si l'utilisateur a tous les rôles
        Blade::if('hasAllRoles', function (...$roles) {
            return Auth::check() && Auth::user()->hasAllRoles($roles);
        });

        // Directive pour vérifier si l'utilisateur a une permission
        Blade::if('hasPermission', function ($menuId, $permissionName) {
            return Auth::check() && Auth::user()->hasPermission($menuId, $permissionName);
        });

        // Directive pour vérifier si l'utilisateur peut accéder à un menu
        Blade::if('canAccessMenu', function ($menuId) {
            return Auth::check() && Auth::user()->canAccessMenu($menuId);
        });

        // Directive pour vérifier si l'utilisateur a une permission par nom
        Blade::if('hasPermissionByName', function ($permissionName) {
            return Auth::check() && Auth::user()->hasPermissionByName($permissionName);
        });

        // Directive pour vérifier si l'utilisateur est administrateur
        Blade::if('isAdmin', function () {
            return Auth::check() && Auth::user()->hasRole('Administrateur');
        });

        // Directive pour vérifier si l'utilisateur est gestionnaire ou administrateur
        Blade::if('isManager', function () {
            return Auth::check() && Auth::user()->hasAnyRole(['Administrateur', 'Gestionnaire']);
        });

        // Directive pour vérifier si l'utilisateur peut créer
        Blade::if('canCreate', function () {
            return Auth::check() && Auth::user()->hasPermissionByName('Créer');
        });

        // Directive pour vérifier si l'utilisateur peut modifier
        Blade::if('canEdit', function () {
            return Auth::check() && Auth::user()->hasPermissionByName('Modifier');
        });

        // Directive pour vérifier si l'utilisateur peut supprimer
        Blade::if('canDelete', function () {
            return Auth::check() && Auth::user()->hasPermissionByName('Supprimer');
        });

        // Directive pour vérifier si l'utilisateur peut lire
        Blade::if('canRead', function () {
            return Auth::check() && Auth::user()->hasPermissionByName('Lire');
        });
    }
}
