@extends('layout.app')

@section('title', 'Modifier Mesure - ' . $indicateur->libelle)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="breadcome-list single-page-breadcome">
                <div class="row">
                    <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                        <div class="breadcome-heading">
                            <h4>Modifier une Mesure</h4>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                        <ul class="breadcome-menu">
                            <li><a href="{{ route('dashboard') }}">Accueil</a> <span class="bread-slash">/</span></li>
                            <li><a href="{{ route('reformes.index') }}">Réformes</a> <span class="bread-slash">/</span></li>
                            <li><a href="{{ route('suivi-indicateurs.index', $reforme->id) }}">Suivi Indicateurs</a> <span class="bread-slash">/</span></li>
                            <li><a href="{{ route('suivi-indicateurs.show', [$reforme->id, $indicateur->id]) }}">{{ Str::limit($indicateur->libelle, 20) }}</a> <span class="bread-slash">/</span></li>
                            <li><span class="bread-blod">Modifier</span></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Informations contextuelles -->
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-warning">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        <i class="fa fa-edit"></i> Modification de Mesure
                    </h3>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Réforme :</strong> {{ $reforme->titre }}</p>
                            <p><strong>Indicateur :</strong> {{ $indicateur->libelle }}</p>
                            <p><strong>Unité de mesure :</strong> <span class="label label-default">{{ $indicateur->unite }}</span></p>
                        </div>
                        <div class="col-md-6">
                            <div class="alert alert-warning">
                                <strong>Mesure à modifier :</strong><br>
                                <i class="fa fa-calendar"></i> {{ $evolution->date_formatee }}<br>
                                <i class="fa fa-line-chart"></i> {{ number_format($evolution->valeur, 2, ',', ' ') }} {{ $indicateur->unite }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages de feedback -->
    @if(session('error'))
        <div class="alert alert-danger alert-dismissible">
            <button type="button" class="close" data-dismiss="alert">&times;</button>
            <i class="fa fa-exclamation-triangle"></i> {{ session('error') }}
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible">
            <button type="button" class="close" data-dismiss="alert">&times;</button>
            <strong>Erreurs de validation :</strong>
            <ul class="mb-0">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <!-- Formulaire de modification -->
    <div class="row">
        <div class="col-lg-8 col-lg-offset-2">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        <i class="fa fa-edit"></i> Modifier la Valeur
                    </h3>
                </div>
                <div class="panel-body">
                    <form action="{{ route('suivi-indicateurs.update', [$reforme->id, $indicateur->id, $evolution->date_evolution]) }}" 
                          method="POST" class="form-horizontal">
                        @csrf
                        @method('PUT')
                        
                        <!-- Date (non modifiable) -->
                        <div class="form-group">
                            <label class="col-sm-3 control-label">Date de mesure</label>
                            <div class="col-sm-9">
                                <p class="form-control-static">
                                    <strong>{{ $evolution->date_formatee }}</strong>
                                    <span class="text-muted">(non modifiable)</span>
                                </p>
                            </div>
                        </div>

                        <!-- Valeur actuelle -->
                        <div class="form-group">
                            <label class="col-sm-3 control-label">Valeur actuelle</label>
                            <div class="col-sm-9">
                                <p class="form-control-static">
                                    <span class="text-primary">
                                        <strong>{{ number_format($evolution->valeur, 2, ',', ' ') }} {{ $indicateur->unite }}</strong>
                                    </span>
                                </p>
                            </div>
                        </div>

                        <!-- Nouvelle valeur -->
                        <div class="form-group {{ $errors->has('valeur') ? 'has-error' : '' }}">
                            <label for="valeur" class="col-sm-3 control-label">
                                Nouvelle valeur <span class="text-danger">*</span>
                            </label>
                            <div class="col-sm-9">
                                <div class="input-group">
                                    <input type="number" 
                                           class="form-control" 
                                           id="valeur" 
                                           name="valeur" 
                                           value="{{ old('valeur', $evolution->valeur) }}"
                                           step="0.01"
                                           min="0"
                                           placeholder="Entrez la nouvelle valeur"
                                           required>
                                    <span class="input-group-addon">{{ $indicateur->unite }}</span>
                                </div>
                                @if($errors->has('valeur'))
                                    <span class="help-block">{{ $errors->first('valeur') }}</span>
                                @endif
                                <span class="help-block">
                                    <i class="fa fa-info-circle"></i> 
                                    Modifiez uniquement si vous avez détecté une erreur dans la saisie précédente.
                                </span>
                            </div>
                        </div>

                        <!-- Commentaire (optionnel) -->
                        <div class="form-group {{ $errors->has('commentaire') ? 'has-error' : '' }}">
                            <label for="commentaire" class="col-sm-3 control-label">
                                Raison de la modification
                            </label>
                            <div class="col-sm-9">
                                <textarea class="form-control" 
                                          id="commentaire" 
                                          name="commentaire" 
                                          rows="3"
                                          placeholder="Expliquez pourquoi vous modifiez cette valeur...">{{ old('commentaire') }}</textarea>
                                @if($errors->has('commentaire'))
                                    <span class="help-block">{{ $errors->first('commentaire') }}</span>
                                @endif
                                <span class="help-block">
                                    <i class="fa fa-info-circle"></i> 
                                    Il est recommandé d'expliquer la raison de la modification.
                                </span>
                            </div>
                        </div>

                        <!-- Boutons d'action -->
                        <div class="form-group">
                            <div class="col-sm-offset-3 col-sm-9">
                                <button type="submit" class="btn btn-warning">
                                    <i class="fa fa-save"></i> Modifier la mesure
                                </button>
                                <a href="{{ route('suivi-indicateurs.show', [$reforme->id, $indicateur->id]) }}" 
                                   class="btn btn-default">
                                    <i class="fa fa-times"></i> Annuler
                                </a>
                                <button type="button" 
                                        class="btn btn-danger pull-right" 
                                        onclick="confirmerSuppression()">
                                    <i class="fa fa-trash"></i> Supprimer cette mesure
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Informations sur l'impact -->
    <div class="row">
        <div class="col-lg-8 col-lg-offset-2">
            <div class="panel panel-info">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        <i class="fa fa-info-circle"></i> Impact de la Modification
                    </h3>
                </div>
                <div class="panel-body">
                    <div class="alert alert-info">
                        <h5><i class="fa fa-lightbulb-o"></i> À savoir :</h5>
                        <ul class="list-unstyled">
                            <li><i class="fa fa-check text-info"></i> La modification affectera les calculs de tendance</li>
                            <li><i class="fa fa-check text-info"></i> Les statistiques seront automatiquement recalculées</li>
                            <li><i class="fa fa-check text-info"></i> Une notification sera envoyée pour tracer la modification</li>
                            <li><i class="fa fa-check text-info"></i> L'historique des modifications est conservé</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h5><i class="fa fa-exclamation-triangle"></i> Attention :</h5>
                        <ul class="list-unstyled">
                            <li><i class="fa fa-warning text-warning"></i> Vérifiez bien la nouvelle valeur avant de valider</li>
                            <li><i class="fa fa-warning text-warning"></i> La date de mesure ne peut pas être modifiée</li>
                            <li><i class="fa fa-warning text-warning"></i> Expliquez la raison de la modification</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="modalSuppression" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Confirmer la suppression</h4>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer cette mesure ?</p>
                <div class="alert alert-danger">
                    <strong>Date :</strong> {{ $evolution->date_formatee }}<br>
                    <strong>Valeur :</strong> {{ number_format($evolution->valeur, 2, ',', ' ') }} {{ $indicateur->unite }}
                </div>
                <p class="text-danger">
                    <i class="fa fa-warning"></i> Cette action est irréversible et affectera les statistiques.
                </p>
            </div>
            <div class="modal-footer">
                <form action="{{ route('suivi-indicateurs.destroy', [$reforme->id, $indicateur->id, $evolution->date_evolution]) }}" 
                      method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">
                        <i class="fa fa-trash"></i> Supprimer définitivement
                    </button>
                </form>
                <button type="button" class="btn btn-default" data-dismiss="modal">
                    <i class="fa fa-times"></i> Annuler
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus sur le champ valeur
    document.getElementById('valeur').focus();
    document.getElementById('valeur').select();
    
    // Validation côté client
    const form = document.querySelector('form');
    const valeurInput = document.getElementById('valeur');
    
    form.addEventListener('submit', function(e) {
        const nouvelleValeur = parseFloat(valeurInput.value);
        const ancienneValeur = {{ $evolution->valeur }};
        
        // Vérifier la valeur
        if (nouvelleValeur < 0) {
            alert('La valeur ne peut pas être négative.');
            e.preventDefault();
            return;
        }
        
        // Demander confirmation si changement important
        const changement = Math.abs((nouvelleValeur - ancienneValeur) / ancienneValeur) * 100;
        if (changement > 50) {
            if (!confirm(`La nouvelle valeur représente un changement de ${changement.toFixed(1)}%. Êtes-vous sûr ?`)) {
                e.preventDefault();
                return;
            }
        }
    });
});

function confirmerSuppression() {
    $('#modalSuppression').modal('show');
}
</script>
@endsection
