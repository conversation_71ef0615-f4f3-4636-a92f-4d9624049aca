<?php

namespace App\Services;

use App\Models\Role;
use App\Models\Menu;
use App\Models\Permission;
use App\Models\PermissionMenu;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PermissionService
{
    /**
     * Assigne automatiquement les permissions à un rôle selon la configuration
     */
    public function assignPermissionsToRole(Role $role)
    {
        $config = config('permissions.default_role_permissions');
        
        if (!isset($config[$role->role_name])) {
            // Si le rôle n'est pas dans la configuration, lui donner des permissions de base
            $this->assignBasicPermissions($role);
            return;
        }

        $roleConfig = $config[$role->role_name];
        
        if ($roleConfig['permissions'] === 'all') {
            // Assigner toutes les permissions
            $this->assignAllPermissions($role);
        } else {
            // Assigner les permissions spécifiques
            $this->assignSpecificPermissions($role, $roleConfig['permissions']);
        }
    }

    /**
     * Assigne toutes les permissions disponibles à un rôle
     */
    public function assignAllPermissions(Role $role)
    {
        $allPermissionMenus = PermissionMenu::all();
        $role->permissionMenus()->sync($allPermissionMenus->pluck('id'));
        
        Log::info("Toutes les permissions assignées au rôle: {$role->role_name}");
    }

    /**
     * Assigne des permissions spécifiques à un rôle
     */
    public function assignSpecificPermissions(Role $role, array $permissions)
    {
        $permissionMenuIds = [];

        foreach ($permissions as $menuUrl => $permissionNames) {
            $menu = Menu::where('url', $menuUrl)->first();
            
            if (!$menu) {
                Log::warning("Menu non trouvé: {$menuUrl}");
                continue;
            }

            foreach ($permissionNames as $permissionName) {
                $permission = Permission::where('permission_name', $permissionName)->first();
                
                if (!$permission) {
                    Log::warning("Permission non trouvée: {$permissionName}");
                    continue;
                }

                $permissionMenu = PermissionMenu::where('menu_id', $menu->id)
                    ->where('permission_id', $permission->id)
                    ->first();

                if ($permissionMenu) {
                    $permissionMenuIds[] = $permissionMenu->id;
                }
            }
        }

        $role->permissionMenus()->sync($permissionMenuIds);
        
        Log::info("Permissions spécifiques assignées au rôle: {$role->role_name}", [
            'permission_count' => count($permissionMenuIds)
        ]);
    }

    /**
     * Assigne des permissions de base à un nouveau rôle
     */
    public function assignBasicPermissions(Role $role)
    {
        // Par défaut, donner accès en lecture au dashboard
        $dashboardMenu = Menu::where('url', '/dashboard')->first();
        $readPermission = Permission::where('permission_name', 'Lire')->first();

        if ($dashboardMenu && $readPermission) {
            $permissionMenu = PermissionMenu::where('menu_id', $dashboardMenu->id)
                ->where('permission_id', $readPermission->id)
                ->first();

            if ($permissionMenu) {
                $role->permissionMenus()->sync([$permissionMenu->id]);
            }
        }

        Log::info("Permissions de base assignées au nouveau rôle: {$role->role_name}");
    }

    /**
     * Récupère toutes les permissions disponibles pour un menu
     */
    public function getAvailablePermissionsForMenu(Menu $menu)
    {
        return $menu->permissionMenus()
            ->with('permission')
            ->get()
            ->pluck('permission.permission_name', 'id');
    }

    /**
     * Récupère les permissions d'un rôle pour un menu spécifique
     */
    public function getRolePermissionsForMenu(Role $role, Menu $menu)
    {
        return $role->permissionMenus()
            ->where('menu_id', $menu->id)
            ->with('permission')
            ->get()
            ->pluck('permission.permission_name');
    }

    /**
     * Vérifie si un rôle a une permission spécifique sur un menu
     */
    public function roleHasPermission(Role $role, $menuId, $permissionName)
    {
        return $role->permissionMenus()
            ->where('menu_id', $menuId)
            ->whereHas('permission', function ($query) use ($permissionName) {
                $query->where('permission_name', $permissionName);
            })
            ->exists();
    }

    /**
     * Crée automatiquement les associations permission-menu pour tous les menus
     */
    public function createPermissionMenusForAllMenus()
    {
        $menus = Menu::all();
        $permissions = Permission::all();

        foreach ($menus as $menu) {
            foreach ($permissions as $permission) {
                PermissionMenu::firstOrCreate([
                    'menu_id' => $menu->id,
                    'permission_id' => $permission->id,
                ]);
            }
        }

        Log::info('Associations permission-menu créées pour tous les menus');
    }

    /**
     * Synchronise les permissions de tous les rôles selon la configuration
     */
    public function syncAllRolePermissions()
    {
        $roles = Role::all();
        
        foreach ($roles as $role) {
            $this->assignPermissionsToRole($role);
        }

        Log::info('Permissions synchronisées pour tous les rôles');
    }

    /**
     * Récupère la configuration des permissions pour un rôle
     */
    public function getRoleConfiguration($roleName)
    {
        $config = config('permissions.default_role_permissions');
        return $config[$roleName] ?? null;
    }

    /**
     * Vérifie si un rôle est un rôle système (ne peut pas être supprimé)
     */
    public function isSystemRole($roleName)
    {
        $systemRoles = config('permissions.system_roles', []);
        return in_array($roleName, $systemRoles);
    }

    /**
     * Récupère tous les menus sensibles
     */
    public function getSensitiveMenus()
    {
        return config('permissions.sensitive_menus', []);
    }
}
