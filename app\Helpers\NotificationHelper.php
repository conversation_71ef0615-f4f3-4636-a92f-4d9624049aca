<?php

namespace App\Helpers;

use App\Models\Notification;
use Illuminate\Support\Facades\Auth;

class NotificationHelper
{
    /**
     * Créer une notification pour l'utilisateur connecté
     */
    public static function create($message, $url = null, $userId = null)
    {
        $userId = $userId ?? Auth::id();
        
        if (!$userId) {
            return false;
        }

        return Notification::create([
            'user_id' => $userId,
            'message' => $message,
            'url' => $url,
            'date_notification' => now(),
            'statut' => 'N'
        ]);
    }

    /**
     * Créer une notification pour tous les utilisateurs
     */
    public static function createForAll($message, $url = null)
    {
        $users = \App\Models\User::all();
        
        foreach ($users as $user) {
            self::create($message, $url, $user->id);
        }
    }

    /**
     * Créer une notification pour des utilisateurs spécifiques
     */
    public static function createForUsers($userIds, $message, $url = null)
    {
        foreach ($userIds as $userId) {
            self::create($message, $url, $userId);
        }
    }

    /**
     * Créer une notification pour les utilisateurs avec un rôle spécifique
     */
    public static function createForRole($roleName, $message, $url = null)
    {
        $users = \App\Models\User::whereHas('roles', function($query) use ($roleName) {
            $query->where('name', $roleName);
        })->get();

        foreach ($users as $user) {
            self::create($message, $url, $user->id);
        }
    }

    /**
     * Marquer toutes les notifications d'un utilisateur comme lues
     */
    public static function markAllAsRead($userId = null)
    {
        $userId = $userId ?? Auth::id();
        
        return Notification::where('user_id', $userId)
            ->where('statut', 'N')
            ->update(['statut' => 'L']);
    }

    /**
     * Obtenir le nombre de notifications non lues pour un utilisateur
     */
    public static function getUnreadCount($userId = null)
    {
        $userId = $userId ?? Auth::id();
        
        return Notification::where('user_id', $userId)
            ->where('statut', 'N')
            ->count();
    }
} 