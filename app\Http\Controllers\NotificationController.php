<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    /**
     * Afficher les notifications de l'utilisateur connecté
     */
    public function index()
    {
        $notifications = Notification::where('user_id', Auth::id())
            ->orderBy('date_notification', 'desc')
            ->limit(10)
            ->get();

        $notificationsNonLues = Notification::where('user_id', Auth::id())
            ->where('statut', 'N')
            ->count();

        return response()->json([
            'notifications' => $notifications,
            'nonLues' => $notificationsNonLues
        ]);
    }

    /**
     * Marquer une notification comme lue
     */
    public function marquerLue(Request $request, $id)
    {
        $notification = Notification::where('user_id', Auth::id())
            ->where('id', $id)
            ->first();

        if ($notification) {
            $notification->marquerCommeLue();
            return response()->json(['success' => true]);
        }

        return response()->json(['success' => false], 404);
    }

    /**
     * Marquer toutes les notifications comme lues
     */
    public function marquerToutesLues()
    {
        Notification::where('user_id', Auth::id())
            ->where('statut', 'N')
            ->update(['statut' => 'L']);

        return response()->json(['success' => true]);
    }

    /**
     * Obtenir le nombre de notifications non lues
     */
    public function countNonLues()
    {
        $count = Notification::where('user_id', Auth::id())
            ->where('statut', 'N')
            ->count();

        return response()->json(['count' => $count]);
    }
} 