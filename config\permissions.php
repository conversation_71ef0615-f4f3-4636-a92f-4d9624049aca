<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Configuration des Permissions par Défaut
    |--------------------------------------------------------------------------
    |
    | Cette configuration définit les permissions par défaut pour chaque rôle.
    | Elle peut être étendue dynamiquement pour supporter de nouveaux rôles
    | sans modification du code source.
    |
    */

    'default_role_permissions' => [
        'Administrateur' => [
            'description' => 'Accès complet à toutes les fonctionnalités du système',
            'permissions' => 'all', // Mot-clé spécial pour toutes les permissions
        ],
        
        'Gestionnaire' => [
            'description' => 'Accès de gestion avec restrictions sur certaines fonctionnalités critiques',
            'permissions' => [
                // Dashboard - Accès complet
                '/dashboard' => ['Créer', 'Lire', 'Modifier', 'Supprimer'],
                
                // Gestion des réformes - Pas de suppression
                '/reforme' => ['Créer', 'Lire', 'Modifier'],
                '/typereforme' => ['Créer', 'Lire', 'Modifier'],
                
                // Activités - Pas de suppression
                '/activites' => ['Créer', 'Lire', 'Modifier'],
                
                // Indicateurs - Pas de suppression
                '/indicateurs' => ['Créer', 'Lire', 'Modifier'],
                
                // Pas d'accès à la gestion des utilisateurs et rôles
                // '/role' => [],
                // '/utilisateurs' => [],
            ],
        ],
        
        'Utilisateur' => [
            'description' => 'Accès en lecture seule aux fonctionnalités de base',
            'permissions' => [
                // Dashboard - Lecture seule
                '/dashboard' => ['Lire'],
                
                // Activités - Lecture seule
                '/activites' => ['Lire'],
                
                // Pas d'accès aux autres fonctionnalités
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Permissions CRUD Disponibles
    |--------------------------------------------------------------------------
    |
    | Liste des permissions CRUD disponibles dans le système.
    | Ces permissions peuvent être assignées à n'importe quel menu.
    |
    */
    'available_permissions' => [
        'Créer' => 'Permet de créer de nouveaux éléments',
        'Lire' => 'Permet de consulter les éléments existants',
        'Modifier' => 'Permet de modifier les éléments existants',
        'Supprimer' => 'Permet de supprimer les éléments existants',
    ],

    /*
    |--------------------------------------------------------------------------
    | Configuration des Rôles Système
    |--------------------------------------------------------------------------
    |
    | Rôles qui ne peuvent pas être supprimés car ils sont critiques
    | pour le fonctionnement du système.
    |
    */
    'system_roles' => [
        'Administrateur',
    ],

    /*
    |--------------------------------------------------------------------------
    | Permissions Spéciales
    |--------------------------------------------------------------------------
    |
    | Permissions qui nécessitent un traitement spécial ou des restrictions
    | particulières.
    |
    */
    'special_permissions' => [
        'super_admin' => [
            'description' => 'Accès super administrateur (bypass toutes les vérifications)',
            'roles' => ['Administrateur'],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Configuration des Menus Sensibles
    |--------------------------------------------------------------------------
    |
    | Menus qui nécessitent des permissions spéciales ou des restrictions
    | particulières.
    |
    */
    'sensitive_menus' => [
        '/role' => [
            'description' => 'Gestion des rôles - Accès administrateur uniquement',
            'min_role_level' => 'Administrateur',
        ],
        '/utilisateurs' => [
            'description' => 'Gestion des utilisateurs - Accès administrateur uniquement',
            'min_role_level' => 'Administrateur',
        ],
    ],
];
