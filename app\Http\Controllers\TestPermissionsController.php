<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Role;
use App\Models\Menu;
use Illuminate\Support\Facades\Hash;

class TestPermissionsController extends Controller
{
    public function index()
    {
        $results = [];
        
        try {
            // Récupérer ou créer les utilisateurs de test
            $adminUser = $this->getOrCreateUser('<EMAIL>', 'Admin', 'Test', 'Administrateur');
            $managerUser = $this->getOrCreateUser('<EMAIL>', 'Manager', 'Test', 'Gestionnaire');
            $regularUser = $this->getOrCreateUser('<EMAIL>', 'User', 'Test', 'Utilisateur');

            // Récupérer les menus
            $activitesMenu = Menu::where('url', '/activites')->first();
            $roleMenu = Menu::where('url', '/role')->first();
            $dashboardMenu = Menu::where('url', '/dashboard')->first();

            if (!$activitesMenu) {
                $results['error'] = 'Menu activités non trouvé';
                return view('test-permissions', compact('results'));
            }

            // Tests pour le menu Activités
            $results['activites'] = [
                'menu_id' => $activitesMenu->id,
                'admin' => [
                    'user' => $adminUser->email,
                    'create' => $adminUser->hasPermission($activitesMenu->id, 'Créer'),
                    'read' => $adminUser->hasPermission($activitesMenu->id, 'Lire'),
                    'update' => $adminUser->hasPermission($activitesMenu->id, 'Modifier'),
                    'delete' => $adminUser->hasPermission($activitesMenu->id, 'Supprimer'),
                ],
                'manager' => [
                    'user' => $managerUser->email,
                    'create' => $managerUser->hasPermission($activitesMenu->id, 'Créer'),
                    'read' => $managerUser->hasPermission($activitesMenu->id, 'Lire'),
                    'update' => $managerUser->hasPermission($activitesMenu->id, 'Modifier'),
                    'delete' => $managerUser->hasPermission($activitesMenu->id, 'Supprimer'),
                ],
                'user' => [
                    'user' => $regularUser->email,
                    'create' => $regularUser->hasPermission($activitesMenu->id, 'Créer'),
                    'read' => $regularUser->hasPermission($activitesMenu->id, 'Lire'),
                    'update' => $regularUser->hasPermission($activitesMenu->id, 'Modifier'),
                    'delete' => $regularUser->hasPermission($activitesMenu->id, 'Supprimer'),
                ],
            ];

            // Tests pour le menu Rôles (si existe)
            if ($roleMenu) {
                $results['roles'] = [
                    'menu_id' => $roleMenu->id,
                    'admin' => [
                        'user' => $adminUser->email,
                        'read' => $adminUser->hasPermission($roleMenu->id, 'Lire'),
                    ],
                    'manager' => [
                        'user' => $managerUser->email,
                        'read' => $managerUser->hasPermission($roleMenu->id, 'Lire'),
                    ],
                    'user' => [
                        'user' => $regularUser->email,
                        'read' => $regularUser->hasPermission($roleMenu->id, 'Lire'),
                    ],
                ];
            }

            // Tests des méthodes de commodité
            $results['convenience_methods'] = [
                'admin_can_create_activites' => $adminUser->canCreateForUrl('/activites'),
                'user_can_create_activites' => $regularUser->canCreateForUrl('/activites'),
                'user_can_read_activites' => $regularUser->canReadForUrl('/activites'),
            ];

        } catch (\Exception $e) {
            $results['error'] = $e->getMessage();
            $results['trace'] = $e->getTraceAsString();
        }

        return view('test-permissions', compact('results'));
    }

    private function getOrCreateUser($email, $nom, $prenom, $roleName)
    {
        $user = User::where('email', $email)->first();
        
        if (!$user) {
            $user = User::create([
                'nom' => $nom,
                'prenom' => $prenom,
                'email' => $email,
                'pwd' => Hash::make('password'),
            ]);

            $role = Role::where('role_name', $roleName)->first();
            if ($role) {
                $user->roles()->attach($role);
            }
        }

        return $user;
    }
}
