@extends('layout.app')

@section('content')

<div class="container-fluid">
    <!-- Breadcrumb -->
    <div class="breadcome-area">
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="breadcome-list">
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <div class="breadcome-heading">
                                    <form role="search" class="sr-input-func">
                                        <input type="text" placeholder="Search..." class="search-int form-control">
                                        <a href="#"><i class="fa fa-search"></i></a>
                                    </form>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <ul class="breadcome-menu">
                                    <li><span class="bread-blod">Dashboard</span></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Analytics Section -->
    <div class="analytics-sparkle-area">
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                    <div class="analytics-sparkle-line reso-mg-b-30">
                        <div class="analytics-content">
                            <h5>Utilisateurs</h5>
                            <h2><span class="counter">{{ $totalUsers ?? 0 }}</span> <span class="tuition-fees">Utilisateurs</span></h2>
                            <span class="text-success">{{ $userPercent ?? 0 }}%</span>
                            <div class="progress m-b-0">
                                <div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow="{{ $userPercent ?? 0 }}" aria-valuemin="0" aria-valuemax="100" style="width:{{ $userPercent ?? 0 }}%;">
                                    <span class="sr-only">{{ $userPercent ?? 0 }}% Complete</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                    <div class="analytics-sparkle-line reso-mg-b-30">
                        <div class="analytics-content">
                            <h5>Réformes</h5>
                            <h2><span class="counter">{{ $totalReformes ?? 0 }}</span> <span class="tuition-fees">Réformes</span></h2>
                            <span class="text-danger">{{ $reformePercent ?? 0 }}%</span>
                            <div class="progress m-b-0">
                                <div class="progress-bar progress-bar-danger" role="progressbar" aria-valuenow="{{ $reformePercent ?? 0 }}" aria-valuemin="0" aria-valuemax="100" style="width:{{ $reformePercent ?? 0 }}%;">
                                    <span class="sr-only">{{ $reformePercent ?? 0 }}% Complete</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                    <div class="analytics-sparkle-line reso-mg-b-30 table-mg-t-pro dk-res-t-pro-30">
                        <div class="analytics-content">
                            <h5>Activités</h5>
                            <h2><span class="counter">{{ $totalActivites ?? 0 }}</span> <span class="tuition-fees">Activités</span></h2>
                            <span class="text-info">{{ $activitePercent ?? 0 }}%</span>
                            <div class="progress m-b-0">
                                <div class="progress-bar progress-bar-info" role="progressbar" aria-valuenow="{{ $activitePercent ?? 0 }}" aria-valuemin="0" aria-valuemax="100" style="width:{{ $activitePercent ?? 0 }}%;">
                                    <span class="sr-only">{{ $activitePercent ?? 0 }}% Complete</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                    <div class="analytics-sparkle-line table-mg-t-pro dk-res-t-pro-30">
                        <div class="analytics-content">
                            <h5>Indicateurs</h5>
                            <h2><span class="counter">{{ $totalIndicateurs ?? 0 }}</span> <span class="tuition-fees">Indicateurs</span></h2>
                            <span class="text-inverse">{{ $indicateurPercent ?? 0 }}%</span>
                            <div class="progress m-b-0">
                                <div class="progress-bar progress-bar-inverse" role="progressbar" aria-valuenow="{{ $indicateurPercent ?? 0 }}" aria-valuemin="0" aria-valuemax="100" style="width:{{ $indicateurPercent ?? 0 }}%;">
                                    <span class="sr-only">{{ $indicateurPercent ?? 0 }}% Complete</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Section pour les réformes par type -->
<div class="product-sales-area mg-tb-30">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12">
                <div class="product-sales-chart">
                    <div class="portlet-title">
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <div class="caption pro-sl-hd">
                                    <span class="caption-subject text-uppercase"><b>Réformes par type</b></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="analytics-rounded mg-b-30">
                                <div class="analytics-rounded-content">
                                    <ul>
                                        @foreach($reformesParType as $type)
                                        <li><span class="text-info">{{ $type->lib }}</span>  <span class="text-success">{{ $type->total }}</span></li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Section pour les activités récentes -->
<div class="product-sales-area mg-tb-30">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12">
                <div class="product-sales-chart">
                    <div class="portlet-title">
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <div class="caption pro-sl-hd">
                                    <span class="caption-subject text-uppercase"><b>Activités récentes</b></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="analytics-edu-wrap">
                                <div class="analytics-edu-wrap-1">
                                    <ul>
                                        @foreach($activitesRecentes as $activite)
                                        <li><span class="tuition-fees">{{ $activite->created_at->format('d/m/Y') }}</span><span class="text-info">{{ $activite->libelle ?? 'Activité' }}</span></li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection