<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RolePermissionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, ...$parameters): Response
    {
        // Vérifier si l'utilisateur est connecté
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'Vous devez être connecté pour accéder à cette page.');
        }

        $user = Auth::user();
        
        // Si aucun paramètre n'est fourni, laisser passer (juste vérification d'authentification)
        if (empty($parameters)) {
            return $next($request);
        }

        // Analyser les paramètres
        foreach ($parameters as $parameter) {
            if ($this->checkParameter($user, $parameter)) {
                return $next($request);
            }
        }

        // Si aucune condition n'est remplie, refuser l'accès
        return $this->accessDenied($request);
    }

    /**
     * Vérifie un paramètre de permission
     */
    private function checkParameter($user, $parameter)
    {
        // Format: role:nom_role ou permission:nom_permission ou menu:id_menu
        if (strpos($parameter, ':') !== false) {
            [$type, $value] = explode(':', $parameter, 2);
            
            switch ($type) {
                case 'role':
                    return $user->hasRole($value);
                    
                case 'permission':
                    return $user->hasPermissionByName($value);
                    
                case 'menu':
                    return $user->canAccessMenu((int)$value);
                    
                case 'any_role':
                    $roles = explode(',', $value);
                    return $user->hasAnyRole($roles);
                    
                case 'all_roles':
                    $roles = explode(',', $value);
                    return $user->hasAllRoles($roles);
            }
        }
        
        // Si pas de format spécial, considérer comme un nom de rôle
        return $user->hasRole($parameter);
    }

    /**
     * Gère le refus d'accès
     */
    private function accessDenied(Request $request)
    {
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Accès refusé. Vous n\'avez pas les permissions nécessaires.',
                'error' => 'access_denied'
            ], 403);
        }

        return redirect()->route('dashboard')
            ->with('error', 'Accès refusé. Vous n\'avez pas les permissions nécessaires pour accéder à cette page.');
    }
}
