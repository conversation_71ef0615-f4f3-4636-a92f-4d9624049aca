<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Role;
use App\Services\PermissionService;

class SyncAhoRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissionService = new PermissionService();
        
        // <PERSON><PERSON><PERSON> le rôle "aho" s'il n'existe pas
        $ahoRole = Role::firstOrCreate(['role_name' => 'aho']);
        
        // Synchroniser ses permissions selon la configuration
        $permissionService->assignPermissionsToRole($ahoRole);
        
        $this->command->info("Rôle 'aho' créé et permissions synchronisées avec succès.");
    }
}
