<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use App\Services\PermissionService;

class User extends Authenticatable
{
    use Notifiable;

    protected $table = 'users';

    protected $fillable = [
        'personne_id',
        'pwd',
        'status',
    ];

    protected $hidden = [
        'pwd',
    ];

    // Utiliser le bon champ de mot de passe
    public function getAuthPassword()
    {
        return $this->pwd;
    }

    public function personne()
    {
        return $this->belongsTo(Personne::class, 'personne_id');
    }

    /**
     * Retourne la personne associée ou lance une exception si absente.
     */
    public function personneOrFail()
    {
        if (!$this->personne) {
            throw new \Exception('Personne liée introuvable pour l\'utilisateur ID=' . $this->id);
        }
        return $this->personne;
    }

    public function roles()
    {
        return $this->belongsToMany(Role::class, 'user_role', 'id_user', 'role_id');
    }

    /**
     * Vérifie si l'utilisateur a un rôle spécifique
     */
    public function hasRole($roleName)
    {
        return $this->roles()->where('role_name', $roleName)->exists();
    }

    /**
     * Vérifie si l'utilisateur a l'une des rôles spécifiés
     */
    public function hasAnyRole($roles)
    {
        if (is_string($roles)) {
            $roles = [$roles];
        }

        return $this->roles()->whereIn('role_name', $roles)->exists();
    }

    /**
     * Vérifie si l'utilisateur a tous les rôles spécifiés
     */
    public function hasAllRoles($roles)
    {
        if (is_string($roles)) {
            $roles = [$roles];
        }

        return $this->roles()->whereIn('role_name', $roles)->count() === count($roles);
    }

    /**
     * Vérifie si l'utilisateur a une permission spécifique sur un menu
     */
    public function hasPermission($menuId, $permissionName)
    {
        return $this->roles()
            ->whereHas('permissionMenus', function ($query) use ($menuId, $permissionName) {
                $query->where('menu_id', $menuId)
                      ->whereHas('permission', function ($subQuery) use ($permissionName) {
                          $subQuery->where('permission_name', $permissionName);
                      });
            })
            ->exists();
    }

    /**
     * Vérifie si l'utilisateur peut créer dans un menu spécifique
     */
    public function canCreate($menuId = null)
    {
        if ($menuId) {
            return $this->hasPermission($menuId, 'Créer');
        }
        return $this->hasPermissionByName('Créer');
    }

    /**
     * Vérifie si l'utilisateur peut modifier dans un menu spécifique
     */
    public function canEdit($menuId = null)
    {
        if ($menuId) {
            return $this->hasPermission($menuId, 'Modifier');
        }
        return $this->hasPermissionByName('Modifier');
    }

    /**
     * Vérifie si l'utilisateur peut supprimer dans un menu spécifique
     */
    public function canDelete($menuId = null)
    {
        if ($menuId) {
            return $this->hasPermission($menuId, 'Supprimer');
        }
        return $this->hasPermissionByName('Supprimer');
    }

    /**
     * Vérifie si l'utilisateur peut lire dans un menu spécifique
     */
    public function canRead($menuId = null)
    {
        if ($menuId) {
            return $this->hasPermission($menuId, 'Lire');
        }
        return $this->hasPermissionByName('Lire');
    }

    /**
     * Vérifie si l'utilisateur peut accéder à un menu
     */
    public function canAccessMenu($menuId)
    {
        return $this->roles()
            ->whereHas('permissionMenus', function ($query) use ($menuId) {
                $query->where('menu_id', $menuId);
            })
            ->exists();
    }

    /**
     * Récupère tous les menus accessibles par l'utilisateur
     */
    public function getAccessibleMenus()
    {
        return \App\Models\Menu::whereHas('permissionMenus', function ($query) {
            $query->whereHas('roles', function ($roleQuery) {
                $roleQuery->whereIn('role.id', $this->roles()->pluck('role.id'));
            });
        })->where('is_active', true)->orderBy('ordre')->get();
    }

    /**
     * Vérifie si l'utilisateur a une permission spécifique (par nom de permission)
     */
    public function hasPermissionByName($permissionName)
    {
        return $this->roles()
            ->whereHas('permissionMenus.permission', function ($query) use ($permissionName) {
                $query->where('permission_name', $permissionName);
            })
            ->exists();
    }

    /**
     * Récupère toutes les permissions de l'utilisateur
     */
    public function getAllPermissions()
    {
        return \App\Models\Permission::whereHas('permissionMenus.roles', function ($query) {
            $query->whereIn('role.id', $this->roles()->pluck('role.id'));
        })->get();
    }

    /**
     * Vérifie les permissions par URL de menu
     */
    public function hasPermissionForUrl($url, $permissionName)
    {
        $menu = \App\Models\Menu::where('url', $url)->first();
        if (!$menu) {
            return false;
        }
        return $this->hasPermission($menu->id, $permissionName);
    }

    /**
     * Méthodes de commodité pour les permissions par URL
     */
    public function canCreateForUrl($url)
    {
        return $this->hasPermissionForUrl($url, 'Créer');
    }

    public function canEditForUrl($url)
    {
        return $this->hasPermissionForUrl($url, 'Modifier');
    }

    public function canDeleteForUrl($url)
    {
        return $this->hasPermissionForUrl($url, 'Supprimer');
    }

    public function canReadForUrl($url)
    {
        return $this->hasPermissionForUrl($url, 'Lire');
    }

    /**
     * Récupère l'ID du menu par URL
     */
    public function getMenuIdByUrl($url)
    {
        $menu = \App\Models\Menu::where('url', $url)->first();
        return $menu ? $menu->id : null;
    }

    /**
     * MÉTHODES GÉNÉRIQUES POUR TOUS LES RÔLES
     */

    /**
     * Méthode générique pour vérifier n'importe quelle permission
     */
    public function can($permission, $menuId = null, $url = null)
    {
        if ($url && !$menuId) {
            $menu = \App\Models\Menu::where('url', $url)->first();
            $menuId = $menu ? $menu->id : null;
        }

        if (!$menuId) {
            return false;
        }

        return $this->hasPermission($menuId, $permission);
    }

    /**
     * Vérifie si l'utilisateur a au moins une des permissions spécifiées
     */
    public function hasAnyPermission(array $permissions, $menuId = null, $url = null)
    {
        foreach ($permissions as $permission) {
            if ($this->can($permission, $menuId, $url)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Vérifie si l'utilisateur a toutes les permissions spécifiées
     */
    public function hasAllPermissions(array $permissions, $menuId = null, $url = null)
    {
        foreach ($permissions as $permission) {
            if (!$this->can($permission, $menuId, $url)) {
                return false;
            }
        }
        return true;
    }

    /**
     * Récupère toutes les permissions de l'utilisateur pour un menu
     */
    public function getPermissionsForMenu($menuId)
    {
        $permissions = [];
        $availablePermissions = config('permissions.available_permissions', []);

        foreach (array_keys($availablePermissions) as $permission) {
            if ($this->hasPermission($menuId, $permission)) {
                $permissions[] = $permission;
            }
        }

        return $permissions;
    }

    /**
     * Récupère toutes les permissions de l'utilisateur pour une URL
     */
    public function getPermissionsForUrl($url)
    {
        $menu = \App\Models\Menu::where('url', $url)->first();
        return $menu ? $this->getPermissionsForMenu($menu->id) : [];
    }

    /**
     * Vérifie si l'utilisateur est super admin (bypass toutes les vérifications)
     */
    public function isSuperAdmin()
    {
        $superAdminRoles = config('permissions.special_permissions.super_admin.roles', []);
        return $this->roles()->whereIn('role_name', $superAdminRoles)->exists();
    }

    /**
     * Vérifie si l'utilisateur a un rôle spécifique
     */
    public function hasRoleByName($roleName)
    {
        return $this->roles()->where('role_name', $roleName)->exists();
    }

    /**
     * Récupère les noms de tous les rôles de l'utilisateur
     */
    public function getRoleNames()
    {
        return $this->roles()->pluck('role_name')->toArray();
    }

    // Les autres relations sessions...
}
