<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Reforme;
use App\Models\Indicateur;
use App\Models\ReformeIndicateur;
use App\Models\EvolutionIndicateur;
use Carbon\Carbon;

class SuiviIndicateurSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Récupérer quelques réformes et indicateurs existants
        $reformes = Reforme::take(3)->get();
        $indicateurs = Indicateur::take(5)->get();

        if ($reformes->isEmpty() || $indicateurs->isEmpty()) {
            $this->command->info('Aucune réforme ou indicateur trouvé. Veuillez d\'abord exécuter les seeders pour les réformes et indicateurs.');
            return;
        }

        foreach ($reformes as $reforme) {
            // Associer 2-3 indicateurs à chaque réforme
            $indicateursReforme = $indicateurs->random(rand(2, 3));

            foreach ($indicateursReforme as $indicateur) {
                // Créer la relation reforme-indicateur
                $reformeIndicateur = ReformeIndicateur::firstOrCreate([
                    'reforme_id' => $reforme->id,
                    'indicateur_id' => $indicateur->id,
                ]);

                // Créer des évolutions sur les 6 derniers mois
                $dateDebut = Carbon::now()->subMonths(6);
                $valeurInitiale = rand(50, 200);

                for ($i = 0; $i < 6; $i++) {
                    $date = $dateDebut->copy()->addMonths($i);

                    // Calculer une progression réaliste
                    $variation = rand(-10, 15); // Variation entre -10% et +15%
                    $valeurInitiale = $valeurInitiale * (1 + $variation / 100);
                    $valeurInitiale = max(0, $valeurInitiale); // Éviter les valeurs négatives

                    EvolutionIndicateur::create([
                        'reforme_indicateur_id' => $reformeIndicateur->id,
                        'date_evolution' => $date->format('Y-m-d'),
                        'valeur' => round($valeurInitiale, 2),
                        'commentaire' => $this->genererCommentaire($variation),
                    ]);
                }
            }
        }

        $this->command->info('Données de test pour le suivi des indicateurs créées avec succès !');
    }

    /**
     * Générer un commentaire basé sur la variation
     */
    private function genererCommentaire($variation): string
    {
        if ($variation > 10) {
            return 'Amélioration significative observée';
        } elseif ($variation > 5) {
            return 'Progression positive';
        } elseif ($variation > 0) {
            return 'Légère amélioration';
        } elseif ($variation > -5) {
            return 'Stabilité relative';
        } elseif ($variation > -10) {
            return 'Légère baisse';
        } else {
            return 'Baisse importante nécessitant attention';
        }
    }
}
